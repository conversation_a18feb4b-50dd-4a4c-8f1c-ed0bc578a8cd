import { adaptAgnoResponse } from './agnoAdapter';

// 使用本地代理，避免CORS问题
const DEFAULT_SSE_URL = `/v1/playground/agents/ds_web_search_agent/runs`;

const SSE_HEADERS = {
  'Cache-Control': 'no-cache',
  'Connection': 'keep-alive',
  'Accept': 'text/event-stream',
};

interface SSEConfig {
  body: any;
  handleMessage: (data: any) => void;
  handleError: (error: Error) => void;
  handleClose: () => void;
}

/**
 * 将请求参数转换为agno系统需要的multipart/form-data格式
 * @param params 原始请求参数
 * @returns FormData对象
 */
function createAgnoFormData(params: any): FormData {
  const formData = new FormData();

  // 字段映射：query -> message, sessionId -> session_id
  formData.append('message', params.query || '');
  formData.append('stream', 'true');
  formData.append('session_id', params.sessionId || '');

  return formData;
}

/**
 * 创建服务器发送事件（SSE）连接，适配agno系统
 * agno系统返回的不是标准SSE格式，而是连续的JSON对象
 * @param config SSE 配置
 * @param url 可选的自定义 URL
 */
export default (config: SSEConfig, url: string = DEFAULT_SSE_URL): void => {
  const { body = null, handleMessage, handleError, handleClose } = config;

  // 将请求体转换为agno系统需要的FormData格式
  const formData = createAgnoFormData(body);

  // 使用fetch而不是fetchEventSource，因为agno不返回标准SSE格式
  fetch(url, {
    method: 'POST',
    credentials: 'omit',
    headers: {
      'Accept': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
    body: formData,
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body reader available');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    function readStream(): Promise<void> {
      return reader.read().then(({ done, value }) => {
        if (done) {
          console.log('� 流结束');
          handleClose();
          return;
        }

        // 将新数据添加到缓冲区
        buffer += decoder.decode(value, { stream: true });

        // 处理缓冲区中的完整JSON对象
        processBuffer();

        // 继续读取
        return readStream();
      });
    }

    function processBuffer() {
      let braceCount = 0;
      let start = 0;

      for (let i = 0; i < buffer.length; i++) {
        if (buffer[i] === '{') {
          braceCount++;
        } else if (buffer[i] === '}') {
          braceCount--;

          if (braceCount === 0) {
            // 找到完整的JSON对象
            const jsonStr = buffer.slice(start, i + 1);
            try {
              console.log('📨 收到agno数据:', jsonStr);
              const parsedData = JSON.parse(jsonStr);
              console.log('📨 解析后的数据:', parsedData);

              // 将agno响应适配为当前系统格式
              const adaptedData = adaptAgnoResponse(parsedData);
              console.log('📨 适配后的数据:', adaptedData);
              handleMessage(adaptedData);
            } catch (error) {
              console.error('解析JSON失败:', error, 'Raw data:', jsonStr);
            }

            start = i + 1;
          }
        }
      }

      // 移除已处理的数据
      buffer = buffer.slice(start);
    }

    return readStream();
  })
  .catch(error => {
    console.error('Fetch error:', error);
    handleError(error);
  });
};

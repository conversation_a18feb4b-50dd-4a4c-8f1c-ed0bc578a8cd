/**
 * agno系统响应数据适配器
 * 将agno的简单事件格式转换为当前系统期望的复杂数据结构
 */

// agno系统的事件类型定义
interface AgnoEvent {
  created_at: number;
  event: string;
  agent_id: string;
  agent_name: string;
  run_id: string;
  session_id: string;
  content?: string;
  content_type?: string;
  thinking?: string;
  model?: string;
  model_provider?: string;
}

// 当前系统期望的响应格式
interface AdaptedResponse {
  finished: boolean;
  resultMap: {
    eventData?: {
      messageType: string;
      resultMap: any;
      messageId: string;
      taskId: string;
    };
  };
  packageType: string;
  status?: string;
}

/**
 * 将agno事件适配为当前系统格式
 * @param agnoEvent agno系统的原始事件
 * @returns 适配后的响应数据
 */
export function adaptAgnoResponse(agnoEvent: AgnoEvent): AdaptedResponse {
  console.log('🔄 适配agno事件:', agnoEvent);

  switch (agnoEvent.event) {
    case 'RunStarted':
      return adaptRunStarted(agnoEvent);

    case 'RunResponseContent':
      return adaptRunResponseContent(agnoEvent);

    case 'RunCompleted':
      return adaptRunCompleted(agnoEvent);

    default:
      // 未知事件类型，返回默认结构
      console.warn('⚠️ 未知的agno事件类型:', agnoEvent.event);
      return createDefaultResponse(false);
  }
}

/**
 * 适配RunStarted事件
 */
function adaptRunStarted(event: AgnoEvent): AdaptedResponse {
  return {
    finished: false,
    resultMap: {
      eventData: {
        messageType: 'task',
        resultMap: {
          task: '正在启动对话...',
          messageType: 'task',
          isFinal: false,
        },
        messageId: event.run_id,
        taskId: event.run_id,
      }
    },
    packageType: 'data'
  };
}

/**
 * 适配RunResponseContent事件
 */
function adaptRunResponseContent(event: AgnoEvent): AdaptedResponse {
  return {
    finished: false,
    resultMap: {
      eventData: {
        messageType: 'task',
        resultMap: {
          messageType: 'task',
          task: event.content || '',
          isFinal: false,
          // 为UI提供必要的默认值
          multiAgent: {
            tasks: [],
            plan: undefined,
            plan_thought: ''
          }
        },
        messageId: event.run_id,
        taskId: event.run_id,
      }
    },
    packageType: 'data'
  };
}

/**
 * 适配RunCompleted事件
 */
function adaptRunCompleted(event: AgnoEvent): AdaptedResponse {
  return {
    finished: true,
    resultMap: {
      eventData: {
        messageType: 'result',
        resultMap: {
          messageType: 'result',
          response: event.content || '',
          isFinal: true,
          // 提供完整的默认数据结构
          multiAgent: {
            tasks: [[{
              messageTime: new Date().toISOString(),
              taskId: event.run_id,
              messageType: 'result',
              resultMap: {
                response: event.content || '',
                isFinal: true,
                messageType: 'result',
                // 为conclusion提供必要的结构
                taskSummary: event.content || '',
                fileList: []
              },
              requestId: event.run_id,
              messageId: event.run_id,
              finish: true,
              isFinal: true,
              id: event.run_id
            } as any]],
            plan: undefined,
            plan_thought: ''
          }
        },
        messageId: event.run_id,
        taskId: event.run_id,
      }
    },
    packageType: 'data'
  };
}

/**
 * 创建默认响应结构
 */
function createDefaultResponse(finished: boolean): AdaptedResponse {
  return {
    finished,
    resultMap: {
      eventData: {
        messageType: 'task',
        resultMap: {
          messageType: 'task',
          isFinal: finished,
          multiAgent: {
            tasks: [],
            plan: undefined,
            plan_thought: ''
          }
        },
        messageId: 'default',
        taskId: 'default',
      }
    },
    packageType: finished ? 'complete' : 'data'
  };
}

/**
 * 为缺失字段提供默认值的工具函数
 */
export function getDefaultValues() {
  return {
    multiAgent: {
      tasks: [],
      plan: undefined,
      plan_thought: ""
    },
    taskStatus: 0,
    files: [],
    deepThink: false,
    plan: undefined,
    conclusion: undefined,
    responseType: "txt",
    forceStop: false,
    loading: false,
    thought: "",
    response: "",
    tip: "正在处理您的请求...",
    planList: []
  };
}
